/*
 * Copyright [2023] MINIEYE
 * Descripttion : 文件描述
 * Author       : z<PERSON><PERSON>opan
 * Date         : 2023-07-05
 */

#include "SocketClient.h"
#include "LogObfuscator.h"
#include <ctype.h>      /* for isprint() */
#include <stdlib.h>     /* for atoi() */
#include <sys/stat.h>   /* for S_IRUSR, S_IWUSR, S_IRGRP, S_IROTH */
#include <fcntl.h>      /* for open() */
#include <sys/uio.h>    /* for writev() */
#include <errno.h>
#include <string.h>
#include <glob.h>
#include <syslog.h>
#include <signal.h>
#include <getopt.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <dirent.h>
#include <unistd.h>
#ifdef __linux__
#   include <linux/limits.h>
#else
#   include <limits.h>
#endif
#include <inttypes.h>
#include <string>
#include <vector>
#include <iostream>
#include <sstream>
/**
 * Print usage information of tool.
 */
static void usage()
{
    fprintf(stderr, "options include:\n"
                    "  -l            Set log level\n"
                    "  -o            Output obfuscated logs (encrypted for privacy)\n"
                    "  -s              Set default filter to silent. Equivalent to filterspec '*:S'\n"
                    "  -f <file>, --file=<file>               Log to file. Default is stdout\n"
                    "  -r <kbytes>, --rotate-kbytes=<kbytes>\n"
                    "                  Rotate log every kbytes. Requires -f option\n"
                    "  -n <count>, --rotate-count=<count>\n"
                    "                  Sets max number of rotated logs to <count>, default 4\n"
                    "  -v <format>, --format=<format>\n"
                    "                  Sets the log print format, where <format> is:\n"
                    "                    brief color epoch long monotonic printable process raw\n"
                    "                    tag thread threadtime time uid usec UTC year zone\n"
                    "  -D, --dividers  Print dividers between each log buffer\n"
                    "  -c, --clear     Clear (flush) the entire log and exit\n"
                    "                  if Log to File specified, clear fileset instead\n"
                    "  -d              Dump the log and then exit (don't block)\n"
                    "  -e <expr>, --regex=<expr>\n"
                    "                  Only print lines where the log message matches <expr>\n"
                    "                  where <expr> is a regular expression\n"
                    // Leave --head undocumented as alias for -m
                    "  -m <count>, --max-count=<count>\n"
                    "                  Quit after printing <count> lines. This is meant to be\n"
                    "                  paired with --regex, but will work on its own.\n"
                    "  --print         Paired with --regex and --max-count to let content bypass\n"
                    "                  regex filter but still stop at number of matches.\n"
                    // Leave --tail undocumented as alias for -t
                    "  -t <count>      Print only the most recent <count> lines (implies -d)\n"
                    "  -t '<time>'     Print most recent lines since specified time (implies -d)\n"
                    "  -T <count>      Print only the most recent <count> lines (does not imply -d)\n"
                    "  -T '<time>'     Print most recent lines since specified time (not imply -d)\n"
                    "                  count is pure numerical, time is 'MM-DD hh:mm:ss.mmm...'\n"
                    "                  'YYYY-MM-DD hh:mm:ss.mmm...' or 'sssss.mmm...' format\n"
                    "  -g, --buffer-size                      Get the size of the ring buffer.\n"
                    "  -G <size>, --buffer-size=<size>\n"
                    "                  Set size of log ring buffer, may suffix with K or M.\n"
                    "  -L, -last       Dump logs from prior to last reboot\n"
                    // Leave security (Device Owner only installations) and
                    // kernel (userdebug and eng) buffers undocumented.
                    "  -b <buffer>, --buffer=<buffer>         Request alternate ring buffer, 'main',\n"
                    "                  'system', 'radio', 'events', 'crash', 'default' or 'all'.\n"
                    "                  Multiple -b parameters or comma separated list of buffers are\n"
                    "                  allowed. Buffers interleaved. Default -b main,system,crash.\n"
                    "  -B, --binary    Output the log in binary.\n"
                    "  -S, --statistics                       Output statistics.\n"
                    "  -p, --prune     Print prune white and ~black list. Service is specified as\n"
                    "                  UID, UID/PID or /PID. Weighed for quicker pruning if prefix\n"
                    "                  with ~, otherwise weighed for longevity if unadorned. All\n"
                    "                  other pruning activity is oldest first. Special case ~!\n"
                    "                  represents an automatic quicker pruning for the noisiest\n"
                    "                  UID as determined by the current statistics.\n"
                    "  -P '<list> ...', --prune='<list> ...'\n"
                    "                  Set prune white and ~black list, using same format as\n"
                    "                  listed above. Must be quoted.\n"
                    "  --pid=<pid>     Only prints logs from the given pid.\n"
                    // Check ANDROID_LOG_WRAP_DEFAULT_TIMEOUT value for match to 2 hours
                    "  --wrap          Sleep for 2 hours or when buffer about to wrap whichever\n"
                    "                  comes first. Improves efficiency of polling by providing\n"
                    "                  an about-to-wrap wakeup.\n");

    fprintf(stderr,"\nfilterspecs are a series of \n"
                   "  <tag>[:priority]\n\n"
                   "where <tag> is a log component tag (or * for all) and priority is:\n"
                   "  T    Verbose (default for <tag>)\n"
                   "  D    Debug (default for '*')\n"
                   "  I    Info\n"
                   "  W    Warn\n"
                   "  E    Error\n"
                   "  C    Critical\n"
                   "  S    Silent (suppress all output)\n"
                   "\n'*' by itself means '*:D' and <tag> by itself means <tag>:V.\n"
                   "If no '*' filterspec or -s on command line, all filter defaults to '*:V'.\n"
                   "eg: '*:S <tag>' prints only <tag>, '<tag>:S' suppresses all <tag> log messages.\n"
                   "\nIf not specified on the command line, filterspec is set from ANDROID_LOG_TAGS.\n"
                   "\nIf not specified with -v on command line, format is set from ANDROID_PRINTF_LOG\n"
                   "or defaults to \"threadtime\"\n\n");
}
typedef struct {
    int32_t lFlag;
    int32_t oFlag;
    std::string lFlagTag;
    int8_t lFlagLevel;
} OptData;


static int8_t convertLevel(const char c) {
    int8_t levelVaule = 0;
        switch (c) {
            case 'T':
            case 't':
            {
                levelVaule = 6;
                break;
            }
            case 'D':
            case 'd':
            {
                levelVaule = 5;
                break;
            }
            case 'I':
            case 'i':
            {
                levelVaule = 4;
                break;
            }
            case 'W':
            case 'w':
            {
                levelVaule = 3;
                break;
            }
            case 'E':
            case 'e':
            {
                levelVaule = 2;
                break;
            }
            case 'C':
            case 'c':
            {
                levelVaule = 1;
                break;
            }
            case 'O':
            case 'o':
            {
                levelVaule = 0;
                break;
            }
            default:
            {
                printf("Unrecognized log level %c\n", c);
                levelVaule = -1;
            }
        }
    return levelVaule;
}

static std::vector<std::string> findFilesWithKeywordInDir(const std::string &dirPath, const std::string &keyword) {
    std::vector<std::string> files;
    DIR *dir = opendir(dirPath.c_str());
    if (dir == nullptr) {
        printf("Failed to open directory %s\n", dirPath.c_str());
        return files;
    }
    struct dirent *entry;
    while ((entry = readdir(dir)) != nullptr) {
        std::string filename(entry->d_name);
        // 跳过'.'和'..'目录项
        if (filename == "." || filename == "..") {
            continue;
        }
        // 构造完整的文件路径
        std::string fullPath = dirPath + "/" + filename;
        // 检查是否是socket文件
        struct stat fileInfo;
        if (stat(fullPath.c_str(), &fileInfo) == 0 && S_ISSOCK(fileInfo.st_mode)) {
            // 检查文件名是否包含关键字
            if (filename.find(keyword) != std::string::npos) {
                files.push_back(filename);
            }
        }
    }
    closedir(dir);
    return files;
}


static int32_t sendLogLevelToSocketServer(const std::string &name, int8_t level) {
    minieye::mlog::SocketClient socketClient;
    minieye::mlog::SocketMsg socketMsg;
    memset(&socketMsg, 0, sizeof(socketMsg));
    socketMsg.cmd = 0x01;
    strncpy(socketMsg.key, "logLevel", sizeof(socketMsg.key));
    socketMsg.value[0] = level;
    return socketClient.sendSocket(name, socketMsg, 100);
}



int32_t main(int32_t argc, char *argv[]) {
    OptData optData;
    optData.lFlag = 0;
    optData.oFlag = 0;
    int32_t c;
    opterr = 0;
    while ((c = getopt(argc, argv, "hlco")) != -1) {
        switch (c) {
            case 'l': {
                optData.lFlag = 1;
                break;
            }
            case 'o': {
                optData.oFlag = 1;
                break;
            }
            case 'c': {
                break;
            }
            case 'h': {
                usage();
                return -1;
            }
            default: {
                // printf("Unrecognized Option %c\n", optopt);
                // usage();
                // return -1;    /*for parasoft */
            }
        }
    }
    if (optData.lFlag) {
        for (int32_t i = optind ; i < argc ; i++) {
            char tag[128] = {0};
            char level[128] = {0};
            int ret = sscanf(argv[i], "%[^:]:%s", tag, level);
            // printf("argv[i] %s [%s] [%s] %d\n", argv[i], tag, level, ret);
            if (ret == 2) {
                if (tag[0] != '*' && tag[0] != 0) {
                    optData.lFlagTag = tag;
                }
                if (level[0] != 0) {
                    optData.lFlagLevel = convertLevel(level[0]);
                    break;
                }
            } else {
                printf("ERROR: Invalid filterspecs %s\n", argv[i]);
            }
        }
        if (optData.lFlagLevel < 0) {
            printf("Error loglevel to set!\n");
        } else {
            if (optData.lFlagTag == "") {
                std::vector<std::string> files = findFilesWithKeywordInDir(SOCKET_DIR, "socket_");
                if (!files.empty()) {
                    for (const auto &file : files) {
                        int32_t ret = sendLogLevelToSocketServer(file, optData.lFlagLevel);
                        if (ret == 0) {
                            printf("Set %s log level: %d\n", file.c_str(), optData.lFlagLevel);
                        }
                    }
                } else {
                    printf("Set log level error, not found tag %s process\n", optData.lFlagTag.c_str());
                }
            } else {
                std::vector<std::string> files = findFilesWithKeywordInDir(SOCKET_DIR, "_" + optData.lFlagTag + "_");
                if (!files.empty()) {
                    for (const auto &file : files) {
                        int32_t ret = sendLogLevelToSocketServer(file, optData.lFlagLevel);
                        if (ret == 0) {
                            printf("Set %s log level: %d\n", file.c_str(), optData.lFlagLevel);
                        }
                    }
                } else {
                    printf("Set log level error, not found tag %s process\n", optData.lFlagTag.c_str());
                }
            }
        }
    } else {
        // TODO(liuyuxing): Let's switch to logcat
        // 构建参数数组
        std::vector<const char*> args;
        args.push_back("logdcat");    // logcat 程序需要存放在统PATH环境变量会包含的目录中
        for (int i = 0; i < argc; i++) {    // 直接捕获所有的参数处理
            // -o不传给logdcat，在下面处理
            if (strcmp(argv[i], "-o") != 0) {
                args.push_back(argv[i]);
            }
        }
        args.push_back(nullptr);
        char *const *argsArray = const_cast<char *const *>(args.data());

        if (optData.oFlag) {
            // 混淆分支
            int pipefd[2];
            if (pipe(pipefd) == -1) {
                fprintf(stderr, "Pipe creation failed\n");
                return 1;
            }

            pid_t pid = fork();
            if (pid < 0) {
                // fork failed
                fprintf(stderr, "Fork failed\n");
                return 1;
            } else if (pid == 0) {
                // child process
                close(pipefd[0]);
                dup2(pipefd[1], STDOUT_FILENO);
                close(pipefd[1]);

                execvp(argsArray[0], const_cast<char *const *>(argsArray));
                return 1;
            } else {
                // parent process
                close(pipefd[1]);

                char buffer[4096];
                std::string line;
                FILE* pipe_read = fdopen(pipefd[0], "r");

                while (fgets(buffer, sizeof(buffer), pipe_read) != nullptr) {
                    std::string logLine(buffer);
                    if (!logLine.empty() && logLine.back() == '\n') {
                        logLine.pop_back();
                    }

                    std::string obfuscated = minieye::mlog::LogObfuscator::obfuscate(logLine);

                    printf("%s\n", obfuscated.c_str());
                    fflush(stdout);
                }

                fclose(pipe_read);
                wait(NULL);
            }
        } else {
            pid_t pid = fork();
            if (pid < 0) {
                // fork failed
                fprintf(stderr, "Fork failed\n");
                return 1;
            } else if (pid == 0) {
                // child process
                // Execute another program in the child process
                execvp(argsArray[0], const_cast<char *const *>(argsArray));
                return 1;
            } else {
                // parent process
                // Wait for the child process to finish
                wait(NULL);
            }
        }
    }
}
